import {
  CardForm,
  type CardFormView,
  useStripe,
} from '@stripe/stripe-react-native';
import React, { useMemo, useState } from 'react';
import {
  Alert,
  Modal,
  ScrollView,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';

import { Cancel, VetAssistIcon } from '@/components/icons';
import TextTypes from '@/components/text-types';
import ModalLoader from '@/components/ui/modal-loader';
import { COLOURS } from '@/constants/colours';

import { paymentSheetServices } from './services';
import styles from './styles';

interface PaymentSheetProps {
  visible: boolean;
  onClose: () => void;
  onSuccessPayment: () => void;
  planType: 'plus' | 'ultimate';
  planPrice: string;
  userEmail?: string;
}

const PaymentSheet: React.FC<PaymentSheetProps> = ({
  visible,
  onClose,
  onSuccessPayment,
  planType,
  planPrice,
  userEmail = '',
}) => {
  const { confirmPayment, createPaymentMethod } = useStripe();
  const [email, setEmail] = useState(userEmail);
  const [cardholderName, setCardholderName] = useState('');
  const [country, setCountry] = useState('United Kingdom');
  const [isProcessing, setIsProcessing] = useState(false);
  const [cardComplete, setCardComplete] = useState(false);
  const [cardLast4, setCardLast4] = useState<string | undefined>(undefined);
  const [cardExpiryMonth, setCardExpiryMonth] = useState<number | undefined>(
    undefined
  );
  const [cardExpiryYear, setCardExpiryYear] = useState<number | undefined>(
    undefined
  );

  const {
    overlay,
    sheet,
    header,
    logo,
    planTitle,
    planPriceText,
    planPriceTextDuration,
    renewalText,
    cardInfo,
    sectionTitle,
    emailInput,
    cardFieldContainer,
    cardholderInput,
    confirmButton,
    confirmButtonDisabled,
    confirmButtonText,
    confirmButtonTextDisabled,
    emailText,
    emailTitle,
  } = styles;

  const cardDetails = useMemo(
    () => ({
      last4: cardLast4,
      expiryMonth: cardExpiryMonth
        ? String(cardExpiryMonth).padStart(2, '0')
        : '',
      expiryYear: cardExpiryYear ? String(cardExpiryYear).slice(-2) : '',
      complete: cardComplete,
    }),
    [cardComplete, cardLast4, cardExpiryMonth, cardExpiryYear]
  );

  const handlePayment = async () => {
    if (!cardDetails.complete || !cardholderName.trim() || !email.trim()) {
      Alert.alert('Error', 'Please fill in all required fields');
      return;
    }

    setIsProcessing(true);
    try {
      // REAL IMPLEMENTATION (currently commented out):
      // const result = await paymentSheetServices.processPayment({
      //   planType,
      //   email,
      //   cardholderName,
      //   country,
      //   cardDetails, // This contains the card info from Stripe CardField
      //   confirmPayment, // Pass the Stripe confirmPayment function
      //   createPaymentMethod, // Pass the Stripe createPaymentMethod function
      // });

      // MOCK IMPLEMENTATION (remove when implementing real payment):
      const result = await paymentSheetServices.processPayment({
        planType,
        email,
        cardholderName,
        country,
        cardDetails,
      });

      if (result.success) {
        onSuccessPayment()
      } else {
        Alert.alert('Error', result.error || 'Payment failed');
      }
    } catch (error) {
      Alert.alert('Error', 'An unexpected error occurred');
    } finally {
      setIsProcessing(false);
    }
  };

  const isFormValid =
    cardDetails.complete && cardholderName.trim() && email.trim();

  return (
    <Modal
      visible={visible}
      animationType='slide'
      transparent
      onRequestClose={onClose}
    >
      <View style={overlay}>
        <View style={sheet}>
          {/* Header */}
          <View style={header}>
            <TouchableOpacity onPress={onClose}>
              <Cancel color={COLOURS.primary} />
            </TouchableOpacity>
          </View>

          <ScrollView showsVerticalScrollIndicator={false}>
            <View>
              <VetAssistIcon style={logo} />
              <TextTypes
                type='h5'
                color={COLOURS.secondaryTint}
                customStyle={planTitle}
              >
                {planType === 'ultimate'
                  ? 'Ultimate Monthly Upgrade Plan'
                  : 'Plus Monthly Upgrade Plan'}
              </TextTypes>
              <TextTypes
                type='h2'
                color={COLOURS.textBlack}
                customStyle={planPriceText}
              >
                {planPrice.split('/')[0]}
                <TextTypes
                  type='body1'
                  color={COLOURS.textBlack}
                  customStyle={planPriceTextDuration}
                >
                  /{planPrice.split('/')[1]}
                </TextTypes>
              </TextTypes>
              <TextTypes
                type='body2'
                color={COLOURS.secondaryTint}
                customStyle={renewalText}
              >
                Your plan renews automatically each month.{'\n'}
                Charges will be made to the card you provide{'\n'}
                today unless updated.
              </TextTypes>
            </View>

            {/* Contact Information */}
            <TextTypes
              type='h4'
              color={COLOURS.textBlack}
              customStyle={sectionTitle}
            >
              Contact information
            </TextTypes>
            <View style={emailInput}>
              <TextTypes
                type='h5'
                color={COLOURS.textBlack}
                customStyle={emailTitle}
              >
                Email
              </TextTypes>
              <TextTypes
                type='body2'
                color={COLOURS.textBlack}
                customStyle={emailText}
              >
                {email}
              </TextTypes>
            </View>

            {/* Payment Method */}
            <TextTypes
              type='h4'
              color={COLOURS.textBlack}
              customStyle={sectionTitle}
            >
              Payment method
            </TextTypes>
            <TextTypes
              type='h5'
              color={COLOURS.secondaryTint}
              customStyle={cardInfo}
            >
              Card information
            </TextTypes>
            <View style={cardFieldContainer}>
              <CardForm
                autofocus
                style={{ width: '100%', height: 180 }}
                preferredNetworks={[1, 4]}
                cardStyle={{
                  placeholderColor: COLOURS.placeHolderTextHex,
                }}
                onFormComplete={(form: CardFormView.Details) => {
                  setCardComplete(form.complete ?? false);
                  setCardLast4(form.last4);
                  setCardExpiryMonth(form.expiryMonth);
                  setCardExpiryYear(form.expiryYear);
                  setCountry(form.country);
                }}
              />
            </View>

            {/* Cardholder Name */}
            <TextTypes
              type='h5'
              color={COLOURS.secondaryTint}
              customStyle={cardInfo}
            >
              Cardholder name
            </TextTypes>
            <TextInput
              style={cardholderInput}
              placeholder='Full name on card'
              value={cardholderName}
              onChangeText={setCardholderName}
            />

            {/* Confirm Button */}
            <TouchableOpacity
              style={[
                confirmButton,
                (!isFormValid || isProcessing) && confirmButtonDisabled,
              ]}
              onPress={handlePayment}
              disabled={!isFormValid || isProcessing}
            >
              <TextTypes
                type='h4'
                color={COLOURS.white}
                customStyle={[
                  confirmButtonText,
                  (!isFormValid || isProcessing) && confirmButtonTextDisabled,
                ]}
              >
                CONFIRM UPGRADE
              </TextTypes>
            </TouchableOpacity>
          </ScrollView>
        </View>
      </View>
      {/* Payment Processing Modal */}
      <ModalLoader
        visible={isProcessing}
        title='Processing payment...'
        subtitle='Securing your emergency consultation'
        showCloseButton={false}
      />
    </Modal>
  );
};

export default PaymentSheet;
