import React from 'react';
import { View, Modal, StatusBar, TouchableOpacity } from 'react-native';
import { SafeAreaView, useSafeAreaInsets } from 'react-native-safe-area-context';
import Loader from '@/components/ui/Loader';
import TextTypes from '@/components/text-types';
import { COLOURS } from '@/constants/colours';
import { Cancel } from '@/components/icons';
import styles from './styles';

interface ModalLoaderProps {
  visible: boolean;
  title: string;
  subtitle: string;
  showCloseButton?: boolean;
  onClose?: () => void;
}

const {
  container,
  closeButtonContainer,
  contentContainer,
  loaderContainer,
  mainHeading,
  subtitle: subtitleStyle,
} = styles;

const ModalLoader: React.FC<ModalLoaderProps> = ({
  visible,
  title,
  subtitle,
  showCloseButton = false,
  onClose,
}) => {
  const insets = useSafeAreaInsets();

  return (
    <Modal
      visible={visible}
      transparent={false}
      animationType="fade"
      statusBarTranslucent
    >
      <SafeAreaView style={container}>
        <StatusBar barStyle="dark-content" backgroundColor={COLOURS.white} />
        
        {/* Close button - top left */}
        {showCloseButton && onClose && (
          <TouchableOpacity 
            style={[closeButtonContainer, {top: insets.top + 20}]}
            onPress={onClose}
          >
            <Cancel color={COLOURS.primary}/>
          </TouchableOpacity>
        )}

        {/* Main content */}
        <View style={contentContainer}>
          {/* Loader */}
          <View style={loaderContainer}>
            <Loader />
          </View>

          {/* Main heading */}
          <TextTypes 
            type="h2" 
            color={COLOURS.primary} 
            customStyle={mainHeading}
          >
            {title}
          </TextTypes>

          {/* Subtitle */}
          <TextTypes 
            type="body2" 
            color={COLOURS.secondaryTint} 
            customStyle={subtitleStyle}
          >
            {subtitle}
          </TextTypes>
        </View>
      </SafeAreaView>
    </Modal>
  );
};

export default ModalLoader;
