{"name": "vet-assist", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "test": "jest --watchAll", "lint": "expo lint", "format": "prettier --write .", "postinstall": "patch-package"}, "jest": {"preset": "jest-expo"}, "dependencies": {"@expo-google-fonts/comfortaa": "^0.4.1", "@expo/vector-icons": "^14.0.2", "@react-native-async-storage/async-storage": "^2.2.0", "@react-native-community/datetimepicker": "8.2.0", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/drawer": "^7.5.3", "@react-navigation/native": "^7.0.14", "@stripe/stripe-react-native": "0.39.0", "expo": "~52.0.38", "expo-blur": "~14.0.3", "expo-camera": "16.1.3", "expo-checkbox": "~4.0.1", "expo-clipboard": "^7.1.4", "expo-constants": "~17.0.8", "expo-file-system": "^18.1.11", "expo-font": "~13.0.4", "expo-haptics": "~14.0.1", "expo-image-picker": "~16.0.6", "expo-jwt": "^1.8.2", "expo-linking": "~7.0.5", "expo-module-scripts": "^4.1.9", "expo-modules-core": "^2.4.2", "expo-router": "~4.0.18", "expo-splash-screen": "~0.29.22", "expo-sqlite": "~15.1.4", "expo-status-bar": "~2.0.1", "expo-symbols": "~0.2.2", "expo-system-ui": "~4.0.8", "expo-web-browser": "~14.0.2", "formik": "^2.4.6", "i18next": "^25.3.2", "jsqr": "^1.4.0", "jwt-decode": "^4.0.0", "lottie-react-native": "7.1.0", "react": "18.3.1", "react-dom": "18.3.1", "react-i18next": "^15.6.0", "react-native": "0.76.7", "react-native-element-dropdown": "^2.12.4", "react-native-gesture-handler": "~2.20.2", "react-native-markdown-display": "^7.0.2", "react-native-reanimated": "~3.16.1", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~4.4.0", "react-native-svg": "15.8.0", "react-native-web": "~0.19.13", "react-native-webview": "13.12.5", "yup": "^1.6.1"}, "devDependencies": {"@babel/core": "^7.25.2", "@trivago/prettier-plugin-sort-imports": "^5.2.2", "@types/jest": "^29.5.12", "@types/react": "~18.3.12", "@types/react-test-renderer": "^18.3.0", "jest": "^29.2.1", "jest-expo": "~52.0.6", "patch-package": "^8.0.0", "postinstall-postinstall": "^2.1.0", "react-test-renderer": "18.3.1", "typescript": "^5.3.3"}, "private": true}